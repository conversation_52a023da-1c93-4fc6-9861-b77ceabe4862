import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useNavigation } from '@react-navigation/native';


const BillCancelViewScreen = ({ route }) => {

    const navigation = useNavigation();

    const {
        saleID,
        printDateTime,
        currentTimeStamp,
        itemTableDetails,
        deliveryCharges,
        roundOffAmount,
        totalAmount,
        discountAmount,
        selectedCustomerType,
        selectedGatewayName,
        customerID,
        customerName,
        customerPhoneNumber,
        customerAddress,
        customerPlace,
        customerCity,
        customerState,
        customerPincode,
        branchId,
        branchName,
        areaName,
        gstNumber,
        foodLicenseNumber,
        branchPincode,
        isFromDirectPrint = false, // New parameter to check if coming from direct print
    } = route.params;

    // Format dates
    const formatBusinessDate = (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
    };

    const formatPrintDateTime = (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        return `${day}/${month}/${year} ${hours}:${minutes}`;
    };

    const hideNosColumn = itemTableDetails.every(item => (item.AltQty ?? 0) === 0);
    const hideWeightColumn = itemTableDetails.every(item => (item.Qty ?? 0) === 0);
    const hideGSTColumn = itemTableDetails.every(item => (item.TaxAmount ?? 0) === 0);

    const calculateTaxDetails = (items) => {
        const taxBreakups = [];

        items.forEach(item => {
            const cgstPercent = item.CGSTPercent || 0;
            const sgstPercent = item.SGSTPercent || 0;
            const cgstAmount = item.CGSTAmount || 0;
            const sgstAmount = item.SGSTAmount || 0;

            if (cgstAmount !== 0 || sgstAmount !== 0) {
                taxBreakups.push({
                    cgstPercent,
                    sgstPercent,
                    cgstAmount,
                    sgstAmount,
                });
            }
        });

        return { taxBreakups };
    };

    const taxDetails = calculateTaxDetails(itemTableDetails);

    return (
        <View style={styles.container}>
            {/* Header */}
            <View style={styles.header}>
                <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
                    <Icon name="arrow-back" size={24} color="#000" />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>Cancel Bill View</Text>
            </View>

            <ScrollView style={styles.scrollView}>
                {/* Company Header */}
                <View style={styles.companyHeader}>
                    <Text style={styles.companyName}>ABIS Exports</Text>
                    <Text style={styles.branchName}>{branchName}</Text>
                    <Text style={styles.areaName}>{areaName}</Text>
                    <Text style={styles.gstNumber}>GST: {gstNumber}</Text>
                    <Text style={styles.foodLicense}>Food License: {foodLicenseNumber}</Text>
                    <Text style={styles.pincode}>PIN: {branchPincode}</Text>
                </View>

                {/* Bill Details */}
                <View style={styles.billDetails}>
                    <View style={styles.billRow}>
                        <Text style={styles.billLabel}>Bill No:</Text>
                        <Text style={styles.billValue}>{saleID}</Text>
                    </View>
                    <View style={styles.billRow}>
                        <Text style={styles.billLabel}>Date:</Text>
                        <Text style={styles.billValue}>{formatBusinessDate(currentTimeStamp)}</Text>
                    </View>
                    <View style={styles.billRow}>
                        <Text style={styles.billLabel}>Time:</Text>
                        <Text style={styles.billValue}>{formatPrintDateTime(printDateTime)}</Text>
                    </View>
                </View>

                {/* Customer Details */}
                <View style={styles.customerDetails}>
                    <Text style={styles.sectionTitle}>Customer Details</Text>
                    <Text style={styles.customerText}>ID: {customerID}</Text>
                    <Text style={styles.customerText}>Name: {customerName}</Text>
                    <Text style={styles.customerText}>Phone: {customerPhoneNumber}</Text>
                    {customerAddress && (
                        <Text style={styles.customerText}>
                            Address: {customerAddress}, {customerPlace}, {customerCity}, {customerState} - {customerPincode}
                        </Text>
                    )}
                </View>

                {/* Items Table */}
                <View style={styles.itemsSection}>
                    <Text style={styles.sectionTitle}>Items</Text>
                    <View style={styles.tableHeader}>
                        <Text style={[styles.tableHeaderText, { flex: 3 }]}>Item</Text>
                        {!hideNosColumn && <Text style={[styles.tableHeaderText, { flex: 1 }]}>Nos</Text>}
                        {!hideWeightColumn && <Text style={[styles.tableHeaderText, { flex: 1 }]}>Kgs</Text>}
                        <Text style={[styles.tableHeaderText, { flex: 1 }]}>Rate</Text>
                        <Text style={[styles.tableHeaderText, { flex: 1 }]}>Amount</Text>
                        {!hideGSTColumn && <Text style={[styles.tableHeaderText, { flex: 1 }]}>GST</Text>}
                    </View>

                    {itemTableDetails.map((item, index) => (
                        <View key={index} style={styles.tableRow}>
                            <Text style={[styles.tableRowText, { flex: 3 }]}>{item.ItemName}</Text>
                            {!hideNosColumn && <Text style={[styles.tableRowText, { flex: 1 }]}>{item.AltQty || 0}</Text>}
                            {!hideWeightColumn && <Text style={[styles.tableRowText, { flex: 1 }]}>{item.Qty || 0}</Text>}
                            <Text style={[styles.tableRowText, { flex: 1 }]}>₹{item.Rate || 0}</Text>
                            <Text style={[styles.tableRowText, { flex: 1 }]}>₹{item.Amount || 0}</Text>
                            {!hideGSTColumn && <Text style={[styles.tableRowText, { flex: 1 }]}>₹{item.TaxAmount || 0}</Text>}
                        </View>
                    ))}
                </View>

                {/* Tax Details */}
                {taxDetails.taxBreakups.length > 0 && (
                    <View style={styles.taxSection}>
                        <Text style={styles.sectionTitle}>Tax Details</Text>
                        {taxDetails.taxBreakups.map((tax, index) => (
                            <View key={index} style={styles.taxRow}>
                                <Text style={styles.taxText}>
                                    CGST ({tax.cgstPercent}%): ₹{tax.cgstAmount.toFixed(2)}
                                </Text>
                                <Text style={styles.taxText}>
                                    SGST ({tax.sgstPercent}%): ₹{tax.sgstAmount.toFixed(2)}
                                </Text>
                            </View>
                        ))}
                    </View>
                )}

                {/* Total Section */}
                <View style={styles.totalSection}>
                    <View style={styles.totalRow}>
                        <Text style={styles.totalLabel}>Subtotal:</Text>
                        <Text style={styles.totalValue}>₹{(parseFloat(totalAmount) - parseFloat(discountAmount || 0)).toFixed(2)}</Text>
                    </View>
                    {parseFloat(discountAmount || 0) > 0 && (
                        <View style={styles.totalRow}>
                            <Text style={styles.totalLabel}>Discount:</Text>
                            <Text style={styles.totalValue}>-₹{parseFloat(discountAmount || 0).toFixed(2)}</Text>
                        </View>
                    )}
                    {parseFloat(deliveryCharges || 0) > 0 && (
                        <View style={styles.totalRow}>
                            <Text style={styles.totalLabel}>Delivery Charges:</Text>
                            <Text style={styles.totalValue}>₹{parseFloat(deliveryCharges || 0).toFixed(2)}</Text>
                        </View>
                    )}
                    {parseFloat(roundOffAmount || 0) !== 0 && (
                        <View style={styles.totalRow}>
                            <Text style={styles.totalLabel}>Round Off:</Text>
                            <Text style={styles.totalValue}>₹{parseFloat(roundOffAmount || 0).toFixed(2)}</Text>
                        </View>
                    )}
                    <View style={[styles.totalRow, styles.grandTotalRow]}>
                        <Text style={styles.grandTotalLabel}>Total:</Text>
                        <Text style={styles.grandTotalValue}>₹{parseFloat(totalAmount || 0).toFixed(2)}</Text>
                    </View>
                </View>

                {/* Payment Details */}
                <View style={styles.paymentSection}>
                    <Text style={styles.sectionTitle}>Payment Details</Text>
                    <Text style={styles.paymentText}>Payment Mode: {selectedGatewayName}</Text>
                    <Text style={styles.paymentText}>Customer Type: {selectedCustomerType}</Text>
                </View>

                {/* Footer */}
                <View style={styles.footer}>
                    <Text style={styles.footerText}>Thank you for your business!</Text>
                    <Text style={styles.footerText}>Visit again!</Text>
                </View>
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
        backgroundColor: '#f8f9fa',
    },
    backButton: {
        marginRight: 16,
    },
    headerTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333',
    },
    scrollView: {
        flex: 1,
        padding: 16,
    },
    companyHeader: {
        alignItems: 'center',
        marginBottom: 20,
        paddingBottom: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },
    companyName: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 4,
    },
    branchName: {
        fontSize: 18,
        fontWeight: '600',
        color: '#555',
        marginBottom: 2,
    },
    areaName: {
        fontSize: 14,
        color: '#666',
        marginBottom: 8,
    },
    gstNumber: {
        fontSize: 12,
        color: '#666',
        marginBottom: 2,
    },
    foodLicense: {
        fontSize: 12,
        color: '#666',
        marginBottom: 2,
    },
    pincode: {
        fontSize: 12,
        color: '#666',
    },
    billDetails: {
        marginBottom: 20,
        paddingBottom: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },
    billRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 4,
    },
    billLabel: {
        fontSize: 14,
        fontWeight: '600',
        color: '#333',
    },
    billValue: {
        fontSize: 14,
        color: '#666',
    },
    customerDetails: {
        marginBottom: 20,
        paddingBottom: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 8,
    },
    customerText: {
        fontSize: 14,
        color: '#666',
        marginBottom: 2,
    },
    itemsSection: {
        marginBottom: 20,
    },
    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#f8f9fa',
        paddingVertical: 8,
        paddingHorizontal: 4,
        borderWidth: 1,
        borderColor: '#e0e0e0',
    },
    tableHeaderText: {
        fontSize: 12,
        fontWeight: 'bold',
        color: '#333',
        textAlign: 'center',
    },
    tableRow: {
        flexDirection: 'row',
        paddingVertical: 8,
        paddingHorizontal: 4,
        borderBottomWidth: 1,
        borderLeftWidth: 1,
        borderRightWidth: 1,
        borderColor: '#e0e0e0',
    },
    tableRowText: {
        fontSize: 12,
        color: '#666',
        textAlign: 'center',
    },
    taxSection: {
        marginBottom: 20,
        paddingBottom: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },
    taxRow: {
        marginBottom: 4,
    },
    taxText: {
        fontSize: 14,
        color: '#666',
        marginBottom: 2,
    },
    totalSection: {
        marginBottom: 20,
        paddingBottom: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },
    totalRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 4,
    },
    totalLabel: {
        fontSize: 14,
        color: '#333',
    },
    totalValue: {
        fontSize: 14,
        color: '#666',
    },
    grandTotalRow: {
        borderTopWidth: 1,
        borderTopColor: '#e0e0e0',
        paddingTop: 8,
        marginTop: 8,
    },
    grandTotalLabel: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
    },
    grandTotalValue: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
    },
    paymentSection: {
        marginBottom: 20,
        paddingBottom: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },
    paymentText: {
        fontSize: 14,
        color: '#666',
        marginBottom: 2,
    },
    footer: {
        alignItems: 'center',
        marginTop: 20,
    },
    footerText: {
        fontSize: 14,
        color: '#666',
        marginBottom: 4,
    },
});

export default BillCancelViewScreen;
