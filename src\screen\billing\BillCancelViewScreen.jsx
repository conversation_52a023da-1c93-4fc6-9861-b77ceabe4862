import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useNavigation } from '@react-navigation/native';


const BillCancelViewScreen = ({ route }) => {

    const navigation = useNavigation();

    const {
        saleID,
        printDateTime,
        currentTimeStamp,
        itemTableDetails,
        deliveryCharges,
        roundOffAmount,
        totalAmount,
        discountAmount,
        selectedCustomerType,
        selectedGatewayName,
        customerID,
        customerName,
        customerPhoneNumber,
        customerAddress,
        customerPlace,
        customerCity,
        customerState,
        customerPincode,
        branchId,
        branchName,
        areaName,
        gstNumber,
        foodLicenseNumber,
        branchPincode,
        isFromDirectPrint = false, // New parameter to check if coming from direct print
    } = route.params;

    // Format dates
    const formatBusinessDate = (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
    };

    const formatPrintDateTime = (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        return `${day}/${month}/${year} ${hours}:${minutes}`;
    };

    const hideNosColumn = itemTableDetails.every(item => (item.AltQty ?? 0) === 0);
    const hideWeightColumn = itemTableDetails.every(item => (item.Qty ?? 0) === 0);
    const hideGSTColumn = itemTableDetails.every(item => (item.TaxAmount ?? 0) === 0);

    const calculateTaxDetails = (items) => {
        const taxBreakups = [];

        items.forEach(item => {
            const cgstPercent = item.CGSTPercent || 0;
            const sgstPercent = item.SGSTPercent || 0;
            const cgstAmount = item.CGSTAmount || 0;
            const sgstAmount = item.SGSTAmount || 0;

            if (cgstAmount !== 0 || sgstAmount !== 0) {
                taxBreakups.push({
                    cgstPercent,
                    sgstPercent,
                    cgstAmount,
                    sgstAmount,
                });
            }
        });

        return { taxBreakups };
    };

    const taxDetails = calculateTaxDetails(itemTableDetails);

    return (
        <View style={styles.container}>
            <ScrollView style={styles.scrollContainer}>
                <Text style={styles.title}>ABIS Exports India Pvt Ltd</Text>
                <Text style={styles.center}>{branchId}, {branchName}</Text>
                <Text style={styles.center}>{areaName}</Text>
                <Text style={styles.center}>{branchPincode}</Text>
                <Text style={styles.center}>FSSAI License Number: {foodLicenseNumber}, GSTIN : {gstNumber}</Text>
                {!isFromDirectPrint && (
                    <Text style={styles.center}>Duplicate Bill</Text>
                )}

                <View style={styles.rowSpaceBetween}>
                    <Text>POS/Bill No: {saleID}</Text>
                    <Text>Print Date: {formatPrintDateTime(printDateTime)}</Text>
                </View>
                <View style={styles.rowRight}>
                    <Text>Business Date: {formatBusinessDate(currentTimeStamp)}</Text>
                </View>

            <Text style={styles.sectionHeader}>Item Details:</Text>
            <View style={styles.table}>
                <View style={styles.tableRow}>
                    <Text style={styles.cell}>Item name</Text>
                    <Text style={styles.cell}>Qty</Text>
                    <Text style={styles.cell}>Rate</Text>
                    {!hideGSTColumn && <Text style={styles.cell}>GST</Text>}
                    <Text style={styles.cell}>Total</Text>
                </View>
                {itemTableDetails.map((item, index) => (
                    <View style={styles.tableRow} key={index}>
                        <Text style={styles.cell}>{item.ItemName}</Text>
                        <Text style={styles.cell}>{item.Qty !== 0 ? item.Qty : item.AltQty}</Text>
                        <Text style={styles.cell}>{item.Rate}</Text>
                        {!hideGSTColumn && <Text style={styles.cell}>{item.TaxAmount}</Text>}
                        <Text style={styles.cell}>{item.TotalAmount}</Text>
                    </View>
                ))}
            </View>

            {deliveryCharges !== 0 && (
                <View style={styles.rowSpaceBetween}>
                    <Text>Freight:</Text>
                    <Text>₹{deliveryCharges}</Text>
                </View>
            )}

            <View style={styles.rowSpaceBetween}>
                <Text>Roundoff:</Text>
                <Text>₹{roundOffAmount}</Text>
            </View>

            <View style={styles.rowSpaceBetween}>
                <Text style={styles.bold}>Total (incl. GST):</Text>
                <Text style={styles.bold}>₹{totalAmount}</Text>
            </View>

            {selectedCustomerType !== 'CS' && (
                <View style={styles.rowSpaceBetween}>
                    <Text>Payment mode:</Text>
                    <Text>{selectedGatewayName}</Text>
                </View>
            )}

            {(discountAmount !== '0' || taxDetails.taxBreakups.length > 0) && <View style={styles.divider} />}

            {discountAmount !== '0' && (
                <View style={styles.rowCenter}>
                    <Text>You saved ₹{discountAmount}</Text>
                </View>
            )}

            {taxDetails.taxBreakups.length > 0 && (
                <>
                    <View style={styles.rowCenter}>
                        <Text style={styles.cell}>CGST</Text>
                        <Text style={styles.cell}>SGST</Text>
                        <Text style={styles.cell}>CGST Amt</Text>
                        <Text style={styles.cell}>SGST Amt</Text>
                    </View>
                    {taxDetails.taxBreakups.map((tax, i) => (
                        <View key={i} style={styles.rowCenter}>
                            <Text style={styles.cell}>{tax.cgstPercent}%</Text>
                            <Text style={styles.cell}>{tax.sgstPercent}%</Text>
                            <Text style={styles.cell}>₹{tax.cgstAmount.toFixed(2)}</Text>
                            <Text style={styles.cell}>₹{tax.sgstAmount.toFixed(2)}</Text>
                        </View>
                    ))}
                </>
            )}

            <Text style={styles.sectionHeader}>Customer Details:</Text>
            <Text>Customer ID: {customerID}</Text>
            <Text>Customer Name: {customerName}</Text>
            <Text>Mobile: {customerPhoneNumber}</Text>
            {customerAddress?.length > 0 && <Text>Address: {customerAddress}</Text>}
            {(customerPlace || customerCity) && <Text>{customerPlace} {customerCity}</Text>}
            {(customerState || customerPincode) && <Text>{customerState} {customerPincode}</Text>}

            <View style={styles.divider} />
            <Text style={styles.center}>Thanks & Visit Again!</Text>

            </ScrollView>

            {/* Bottom Back Button */}
            <View style={styles.bottomButtonContainer}>
                <TouchableOpacity
                    style={styles.backButton}
                    onPress={() => navigation.navigate('Billing')}
                >
                    <Text style={styles.backButtonText}>Back</Text>
                </TouchableOpacity>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    scrollContainer: {
        padding: 16,
        paddingBottom: 100, // Add space for bottom button
    },
    bottomButtonContainer: {
        position: 'absolute',
        bottom: 20,
        left: 20,
        right: 20,
        alignItems: 'center',
    },
    backButton: {
        backgroundColor: '#dc3545',
        paddingVertical: 15,
        paddingHorizontal: 40,
        borderRadius: 10,
        minWidth: 120,
        minHeight: 60,
        justifyContent: 'center',
        alignItems: 'center',
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
    },
    backButtonText: {
        color: '#fff',
        fontSize: 18,
        fontWeight: 'bold',
    },
    title: { fontSize: 18, fontWeight: 'bold', textAlign: 'center' },
    center: { textAlign: 'center' },
    rowSpaceBetween: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginVertical: 4,
    },
    rowRight: { flexDirection: 'row', justifyContent: 'flex-end' },
    sectionHeader: {
        marginTop: 16,
        fontWeight: 'bold',
        fontSize: 16,
    },
    table: {
        borderWidth: 1,
        marginTop: 8,
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
    },
    cell: {
        flex: 1,
        padding: 6,
        textAlign: 'center',
        borderRightWidth: 1,
    },
    bold: { fontWeight: 'bold' },
    rowCenter: {
        flexDirection: 'row',
        justifyContent: 'center',
    },
    divider: {
        marginVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#ccc',
    },
});

export default BillCancelViewScreen;
