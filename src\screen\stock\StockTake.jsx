import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TextInput,
    TouchableOpacity,
    ScrollView,
    FlatList,
    Modal,
    Alert,
    ActivityIndicator,
    Platform
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import Icon from 'react-native-vector-icons/Feather';
import DateTimePicker from '@react-native-community/datetimepicker';
import Navbar from '../../components/Navbar';
import { useNavigation } from '@react-navigation/native';

import { fetchItemList } from '../../apiHandling/StockAPI/fetchItemListAPI';
import { fetchItemDetail, fetchItemStock } from '../../apiHandling/StockAPI/itemDetailsStockAPI';
import { saveStockAdjustment, fetchBusinessDate } from '../../apiHandling/StockAPI/saveStockAdjustAPI';
import { fetchStockTakeList } from '../../apiHandling/StockAPI/fetchStockTakeListAPI';
import { fetchStockTakeDetail } from '../../apiHandling/StockAPI/fetchStockTakeDetailAPI';

import AsyncStorage from '@react-native-async-storage/async-storage';


const ports = [
    { label: 'COM 1', value: 'COM 1' },
    { label: 'COM 2', value: 'COM 2' },
    { label: 'COM 3', value: 'COM 3' },
];

const StockTake = ({ navigation }) => {
    const navigationHook = useNavigation();
    // Main state variables
    const [items, setItems] = useState([]);
    const [selectedItems, setSelectedItems] = useState([]);

    // Form input states
    const [itemName, setItemName] = useState('');
    const [itemId, setItemId] = useState('');
    const [itemList, setItemList] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [searchText, setSearchText] = useState('');
    const [batchEnabled, setBatchEnabled] = useState(false);
    const [sellByWeight, setSellByWeight] = useState(false);
    const [altQtyEnabled, setAltQtyEnabled] = useState(false);
    const [stockQty, setStockQty] = useState(0);
    const [stockAltQty, setStockAltQty] = useState(0);
    const [batchList, setBatchList] = useState([]);
    const [batchModalVisible, setBatchModalVisible] = useState(false);
    const [batch, setBatch] = useState('');
    const [newNos, setNewNos] = useState('');
    const [newWt, setNewWt] = useState('');
    const [adjustedNos, setAdjustedNos] = useState('');
    const [adjustedWt, setAdjustedWt] = useState('');
    const [port, setPort] = useState(null);
    const [nos, setNos] = useState('');
    const [kgs, setKgs] = useState('');
    const [remarks, setRemarks] = useState('');
    const [itemDetail, setItemDetail] = useState(null);

    // View modal states
    const [viewModalVisible, setViewModalVisible] = useState(false);
    const [stockTakeList, setStockTakeList] = useState([]);
    const [filteredStockTakes, setFilteredStockTakes] = useState([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [fromDate, setFromDate] = useState(null);
    const [toDate, setToDate] = useState(null);
    const [showFromDatePicker, setShowFromDatePicker] = useState(false);
    const [showToDatePicker, setShowToDatePicker] = useState(false);
    const [loading, setLoading] = useState(false);

    // Edit functionality states
    const [editingItemIndex, setEditingItemIndex] = useState(null);



    useEffect(() => {
        // Load item list when screen mounts
        loadItemList();
    }, []);

    // Function to open view modal & fetch initial stock takes
    const openViewModal = async () => {
        setLoading(true);
        const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;

        try {
            const businessDate = await fetchBusinessDate(bearerToken, loginBranchID);
            const businessDateObj = new Date(businessDate);
            const from = new Date(businessDateObj);
            const to = new Date(businessDateObj);
            to.setDate(to.getDate() + 1); // Next day

            setFromDate(from);
            setToDate(to);

            const fromDateStr = from.toISOString().split('T')[0].replace(/-/g, '');
            const toDateStr = to.toISOString().split('T')[0].replace(/-/g, '');

            const stockTakes = await fetchStockTakeList(bearerToken, loginBranchID, fromDateStr, toDateStr);

            // Sort by DocID in increasing order
            const sortedStockTakes = stockTakes.sort((a, b) => {
                const docIdA = parseInt(a.DocID) || 0;
                const docIdB = parseInt(b.DocID) || 0;
                return docIdA - docIdB;
            });

            setStockTakeList(sortedStockTakes);
            setFilteredStockTakes(sortedStockTakes);
            setViewModalVisible(true);
        } catch (err) {
            console.error('Error fetching stock takes:', err);
        }
        setLoading(false);
    };

    // Filter stock takes when search query changes
    useEffect(() => {
        if (!searchQuery) {
            setFilteredStockTakes(stockTakeList);
        } else {
            const filtered = stockTakeList.filter(stockTake =>
                stockTake.DocID.toLowerCase().includes(searchQuery.toLowerCase()) ||
                stockTake.ISO_Number.toLowerCase().includes(searchQuery.toLowerCase())
            );
            setFilteredStockTakes(filtered);
        }
    }, [searchQuery, stockTakeList]);

    // Fetch stock takes when date changes
    useEffect(() => {
        const fetchStockTakesBySelectedDates = async () => {
            if (!fromDate || !toDate || !viewModalVisible) return;

            setLoading(true);
            const bearerToken = await AsyncStorage.getItem('authToken');
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const parsedBranch = JSON.parse(selectedBranch);
            const loginBranchID = parsedBranch.BranchId;

            try {
                const fromDateStr = fromDate.toISOString().split('T')[0].replace(/-/g, '');
                const toDateStr = toDate.toISOString().split('T')[0].replace(/-/g, '');

                const stockTakes = await fetchStockTakeList(bearerToken, loginBranchID, fromDateStr, toDateStr);

                // Sort by DocID in increasing order
                const sortedStockTakes = stockTakes.sort((a, b) => {
                    const docIdA = parseInt(a.DocID) || 0;
                    const docIdB = parseInt(b.DocID) || 0;
                    return docIdA - docIdB;
                });

                setStockTakeList(sortedStockTakes);
                setFilteredStockTakes(sortedStockTakes);
            } catch (err) {
                console.error('Error fetching stock takes by date:', err);
            }
            setLoading(false);
        };

        fetchStockTakesBySelectedDates();
    }, [fromDate, toDate, viewModalVisible]);

    // Handle stock take selection and navigate to view screen
    const handleStockTakeSelect = (stockAdjustId) => {
        setViewModalVisible(false);
        navigationHook.navigate('ViewStockTakeScreen', { stockAdjustId });
    };

    const loadItemList = async () => {
        const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;
        const data = await fetchItemList(bearerToken, loginBranchID);
        setItemList(data);
    };



    // Function to handle item row click for editing (following indent screen pattern)
    const handleItemRowClick = (item, index) => {
        setEditingItemIndex(index);
        setItemName(item.itemName);
        setItemId(item.itemId);
        setBatch(item.batch);
        setStockQty(item.stockQty);
        setStockAltQty(item.stockAltQty);
        setNewNos(item.newNos);
        setNewWt(item.newKgs);
        setRemarks(item.remarks || '');
        setBatchEnabled(item.batchEnabled);
        setSellByWeight(item.sellByWeight);
        setAltQtyEnabled(item.altQtyEnabled);

        // Set item detail if available
        if (item.itemFamilyID || item.stockGroupId) {
            setItemDetail({
                ItemFamilyID: item.itemFamilyID,
                StockGroupId: item.stockGroupId,
                StockGroupName: item.stockGroupName,
                SellByWeight: item.sellByWeight,
                AltQtyEnabled: item.altQtyEnabled,
                BatchEnabled: item.batchEnabled,
                Clubbed: item.clubbed
            });
        }
    };

    const handleSelectPress = () => {
        // Reset all item-related state
        setItemName('');
        setItemId('');
        setBatch('');
        setBatchList([]);
        setStockQty(0);
        setStockAltQty(0);
        setNos('');
        setKgs('');
        setBatchEnabled(false);
        setSellByWeight(false);
        setAltQtyEnabled(false);
        setSearchText('');

        // Open item selection modal
        setModalVisible(true);
    };


    const handleItemSelect = async (item) => {
        setItemName(item.ItemName);
        setItemId(item.ItemID);
        setModalVisible(false);
        const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;

        const detail = await fetchItemDetail(bearerToken, item.ItemID);

        setItemDetail(detail);


        if (!detail) {
            // console.log("Item detail is undefined. Defaulting to Nos enabled and 0.");

            // Default assumption: batch is disabled, sell by weight is false, alt qty is false
            setBatchEnabled(false);
            setSellByWeight(false);
            setAltQtyEnabled(false);

            // Set stock and fields to 0
            setStockQty(0);
            setStockAltQty(0);
            setNos('0');
            setKgs('0');
            setBatchList([]);
            setBatch('');
            return;
        }

        const {
            BatchEnabled,
            SellByWeight,
            AltQtyEnabled,
        } = detail;

        //console.log("Item Detail Flags:", { BatchEnabled, SellByWeight, AltQtyEnabled });

        setBatchEnabled(BatchEnabled);
        setSellByWeight(SellByWeight);
        setAltQtyEnabled(AltQtyEnabled);

        const stockData = await fetchItemStock(bearerToken, loginBranchID, item.ItemID);
        //console.log("Stock Data:", stockData);

        if (BatchEnabled) {
            //console.log("Batch is enabled. Setting batch list and resetting fields.");
            setBatchList(stockData);
            setBatch('');
            setStockQty(0);
            setStockAltQty(0);

        } else if (Array.isArray(stockData) && stockData.length > 0) {
            const stock = stockData[0];
            const qty = stock.StockQty || 0;
            const altQty = stock.StockAltQty || 0;

            setStockQty(qty);
            setStockAltQty(altQty);

            //console.log("Setting stock values from stockData:", { qty, altQty });

            if (SellByWeight && AltQtyEnabled) {
                setKgs(qty.toString());
                setNos(altQty.toString());
            } else if (SellByWeight) {
                setKgs(qty.toString());

            } else {
                setNos(qty.toString());

            }
        } else {
            // Empty or invalid stockData
            // console.log("Empty or invalid stockData. Setting all to 0.");

            setStockQty(0);
            setStockAltQty(0);

            if (SellByWeight && AltQtyEnabled) {
                setKgs('0');
                setNos('0');
            } else if (SellByWeight) {
                setKgs('0');

            } else {
                setNos('0');

            }
        }
    };

    useEffect(() => {
        // Adjusted Nos = New Nos - Available Nos
        const availableNos = parseFloat(nos) || 0;
        const enteredNos = parseFloat(newNos) || 0;
        const adjusted = enteredNos - availableNos;
        setAdjustedNos(adjusted.toString());
    }, [newNos, nos]);

    useEffect(() => {
        // Adjusted Wt = New Wt - Available Kgs
        const availableKgs = parseFloat(kgs) || 0;
        const enteredKgs = parseFloat(newWt) || 0;
        const adjusted = enteredKgs - availableKgs;
        setAdjustedWt(adjusted.toString());
    }, [newWt, kgs]);

    const handleAddItem = () => {
        if (!itemId || !itemName) return Alert.alert('Error', 'Select an item');
        if (sellByWeight && !newWt) return Alert.alert('Error', 'Enter New Kgs');
        if (!sellByWeight && !altQtyEnabled && !newNos) return Alert.alert('Error', 'Enter New Nos');
        if (sellByWeight && altQtyEnabled && (!newWt || !newNos)) return Alert.alert('Error', 'Enter both New Nos and Kgs');

        if (editingItemIndex !== null) {
            // Update existing item (following indent screen pattern)
            const updatedItems = [...items];
            updatedItems[editingItemIndex] = {
                ...updatedItems[editingItemIndex],
                itemId,
                itemName,
                batch,
                stockAltQty,
                stockQty,
                newNos,
                newKgs: newWt,
                remarks,
                stockGroupId: itemDetail?.StockGroupId || '',
                stockGroupName: itemDetail?.StockGroupName || '',
                itemFamilyID: itemDetail?.ItemFamilyID || '',
                batchEnabled,
                sellByWeight,
                altQtyEnabled,
                clubbed: itemDetail?.Clubbed ?? true
            };
            setItems(updatedItems);
            setEditingItemIndex(null);
        } else {
            // Check for duplicate items
            const isDuplicate = items.some(
                (entry) => entry.itemId === itemId && entry.batch === batch
            );
            if (isDuplicate) return Alert.alert('Error', 'Item already added with same batch');

            const newItem = {
                lineNo: String(items.length + 1).padStart(3, '0'),
                itemId,
                itemName,
                batch,
                stockAltQty,
                stockQty,
                newNos,
                newKgs: newWt,
                remarks,
                id: Date.now(), // Unique row ID
                selected: false,

                // Additional fields required for save API
                stockGroupId: itemDetail?.StockGroupId || '',
                stockGroupName: itemDetail?.StockGroupName || '',
                itemFamilyID: itemDetail?.ItemFamilyID || '',
                batchEnabled,
                sellByWeight,
                altQtyEnabled,
                clubbed: itemDetail?.Clubbed ?? true
            };

            setItems(prev => [...prev, newItem]);
        }
        handleClearFields(); // Reset form after add/update
    };


    const handleClearFields = () => {
        setItemName('');
        setItemId('');
        setBatch('');
        setBatchList([]);
        setStockQty(0);
        setStockAltQty(0);
        setNos('');
        setKgs('');
        setNewNos('');
        setNewWt('');
        setRemarks('');
        setBatchEnabled(false);
        setSellByWeight(false);
        setAltQtyEnabled(false);
        setPort(null);
        setItemDetail(null);
        setEditingItemIndex(null); // Reset editing state
    };

    const handleResetAndReload = () => {
        // Reset all form fields
        setItemName('');
        setItemId('');
        setBatch('');
        setBatchList([]);
        setStockQty(0);
        setStockAltQty(0);
        setNos('');
        setKgs('');
        setNewNos('');
        setNewWt('');
        setRemarks('');
        setBatchEnabled(false);
        setSellByWeight(false);
        setAltQtyEnabled(false);
        setPort(null);
        setItemDetail(null);
        setAdjustedNos('');
        setAdjustedWt('');
        setSearchText('');

        // Reset table data
        setItems([]);
        setSelectedItems([]);

        // Reset modal states
        setModalVisible(false);
        setBatchModalVisible(false);

        // Reload item list
        loadItemList();
    };


    const deleteSelectedItems = () => {
        const filtered = items.filter(item => !selectedItems.includes(item.id));
        const reIndexed = filtered.map((item, index) => ({
            ...item,
            lineNo: String(index + 1).padStart(3, '0'),
        }));
        setItems(reIndexed);
        setSelectedItems([]);
    };
    const getISTISOString = () => {
        const now = new Date();

        // Offset IST (+5:30) in milliseconds
        const istOffset = 5.5 * 60 * 60 * 1000;
        const istTime = new Date(now.getTime() + istOffset);

        // Format as ISO string (will still end in Z, but value is IST)
        return istTime.toISOString(); // Includes milliseconds and Z
    };

    const handleSave = async () => {
        const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;
        const userDetails = await AsyncStorage.getItem('userData');
        const user = JSON.parse(userDetails);
        const loginUserID = user.userId;
        try {
            const businessDate = await fetchBusinessDate(bearerToken, loginBranchID);
            const systemDate = getISTISOString();

            const payload = {
                stockAdjustId: '',
                branchId: loginBranchID,
                transTypeId: 'SALE',
                tranSubTypeId: 'CASH',
                businessDate: businessDate,
                remarks: '', // Add if user remarks exist
                isO_number: '',
                deleted: 'N',
                posted: true,
                wScale: 0,
                createdUserId: loginUserID,
                createdDate: systemDate,
                modifiedUserId: '',
                modifiedDate: '1753-01-01T00:00:00.000Z',
                deletedUserId: '',
                deletedDate: '1753-01-01T00:00:00.000Z',
                postedUserID: 'string',
                postedDate: '1753-01-01T00:00:00.000Z',
                sADetails: items.map(item => {
                    // Calculate new quantity and adjusted quantity based on item type
                    const newNos = parseFloat(item.newNos || '0');
                    const newKgs = parseFloat(item.newKgs || '0');
                    const stockQty = parseFloat(item.stockQty || '0');
                    const stockAltQty = parseFloat(item.stockAltQty || '0');

                    let newQty = 0;
                    let adjustedQty = 0;

                    if (item.sellByWeight && item.altQtyEnabled) {
                        // Both weight and nos enabled - weight takes priority for newQty
                        newQty = newKgs;
                        adjustedQty = newKgs - stockQty; // adjusted weight = new weight - stock weight
                    } else if (item.sellByWeight) {
                        // Only weight enabled
                        newQty = newKgs;
                        adjustedQty = newKgs - stockQty; // adjusted weight = new weight - stock weight
                    } else {
                        // Only nos enabled (default case)
                        newQty = newNos;
                        adjustedQty = newNos - stockQty; // adjusted nos = new nos - stock nos
                    }

                    const newAltQty = 0; // always 0 as per requirement

                    return {
                        lineNumber: item.lineNo,
                        itemName: item.itemName,
                        batchNumber: item.batch,
                        stockAltQty: item.stockAltQty,
                        stockQty: item.stockQty,
                        newAltQty: newAltQty,
                        newQty: newQty,
                        nos: item.newNos,
                        kgs: item.newKgs,
                        remarks: item.remarks,
                        groupStockQty: 0,
                        stockGroupId: item.stockGroupId || '', // fetched via item detail
                        stockGroupName: item.stockGroupName || '',
                        itemStatusId: 'OK',
                        itemFamilyID: item.itemFamilyID || '',
                        binId: 'OKBIN',
                        batchEnabled: item.batchEnabled || false,
                        sellByWeight: item.sellByWeight || false,
                        altQtyEnabled: item.altQtyEnabled || false,
                        altQty: 0, // always 0 as per requirement
                        qty: adjustedQty, // This is the adjusted quantity (new - stock)
                        wScale: 0,
                        itemID: item.itemId,
                        clubbed: item.clubbed ?? true,
                        deleted: 'N',
                        createdUserId: loginUserID,
                        createdDate: systemDate,
                        modifiedUserId: 'string',
                        modifiedDate: '1753-01-01T00:00:00.000Z',
                        deletedUserId: 'string',
                        deletedDate: '1753-01-01T00:00:00.000Z',
                        dml: 'i'
                    };
                })
            };

            const result = await saveStockAdjustment(bearerToken, payload);

            if (result.result === 1) {
                // Show success dialog with print option (following indent screen pattern)
                Alert.alert(
                    'Success',
                    `Stock take saved successfully\nStock Adjust ID: ${result.description}\n\nDo you want to print the stock take?`,
                    [
                        {
                            text: 'No',
                            onPress: () => {
                                handleResetAndReload();
                            },
                            style: 'cancel'
                        },
                        {
                            text: 'Yes',
                            onPress: () => {
                                // Reset page first, then navigate
                                handleResetAndReload();
                                fetchLatestStockTakeAndNavigate(result.description);
                            }
                        }
                    ]
                );
            } else {
                Alert.alert('Save Failed', result.description || 'Unexpected response from server.');
            }
        } catch (error) {
            Alert.alert('Error', 'Failed to save stock adjustment.');
        }
    };

    // Function to fetch latest stock take and navigate to view screen (following indent screen pattern)
    const fetchLatestStockTakeAndNavigate = async (stockAdjustId) => {
        try {
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const parsedBranch = JSON.parse(selectedBranch);
            const loginBranchID = parsedBranch.BranchId;

            // Navigate to ViewStockTakeScreen with the saved stock take details
            navigationHook.navigate('ViewStockTakeScreen', {
                stockAdjustId: stockAdjustId
            });
        } catch (error) {
            console.error('Error navigating to view stock take:', error);
            Alert.alert('Error', 'Failed to navigate to view stock take');
            handleResetAndReload();
        }
    };










    return (
        <View style={styles.container}>
            <Navbar />
            <ScrollView contentContainerStyle={styles.scrollContent}>
                {/* Header */}
                <View style={styles.headerRow}>
                    <Text style={styles.headerTitle}>STOCK TAKE</Text>
                    <View style={styles.actionButtons}>
                        <TouchableOpacity style={styles.newBtn} onPress={handleResetAndReload}>
                            <Icon name="user-plus" size={16} color="#000" />
                            <Text style={styles.actionText}> New</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.viewBtn} onPress={openViewModal}>
                            <Icon name="eye" size={16} color="#000" />
                            <Text style={styles.actionText}> View</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={[styles.saveBtn, items.length === 0 && styles.saveBtnDisabled]}
                            onPress={items.length > 0 ? handleSave : null}
                            disabled={items.length === 0}
                        >
                            <Text style={[styles.saveText, items.length === 0 && styles.saveTextDisabled]}>Save</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.cancelBtn} onPress={handleResetAndReload}>
                            <Text style={styles.cancelText}>Cancel</Text>
                        </TouchableOpacity>
                    </View>
                </View>

                {/* Item Details */}
                <View style={styles.section}>
                    <Text style={styles.sectionTitle}>Item Details</Text>

                    <View style={styles.inputRow}>
                        <TextInput
                            placeholder="Item Name"
                            style={styles.inputBox}
                            value={itemName}
                            onChangeText={setItemName}
                        />
                        <TouchableOpacity style={styles.selectBtn} onPress={handleSelectPress}>
                            <Text style={styles.btnTextWhite}>Select</Text>
                        </TouchableOpacity>


                        {/* Item Selection Modal (following indent screen pattern) */}
                        <Modal visible={modalVisible} transparent animationType="slide">
                            <View
                                style={{
                                    flex: 1,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                                }}
                            >
                                <View
                                    style={{
                                        width: '90%',
                                        maxHeight: '80%',
                                        backgroundColor: 'white',
                                        borderRadius: 10,
                                        padding: 16,
                                        position: 'relative',
                                    }}
                                >
                                    {/* Header */}
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            marginBottom: 16,
                                        }}
                                    >
                                        <Text style={{ fontSize: 18, fontWeight: 'bold' }}>Select Item</Text>
                                    </View>

                                    {/* Search Input */}
                                    <TextInput
                                        style={{
                                            borderWidth: 1,
                                            borderColor: '#ccc',
                                            borderRadius: 8,
                                            paddingHorizontal: 12,
                                            paddingVertical: 8,
                                            marginBottom: 16,
                                            fontSize: 16,
                                        }}
                                        placeholder="Search items..."
                                        value={searchText}
                                        onChangeText={setSearchText}
                                    />

                                    {/* Filtered ScrollView with 4-column layout (following indent screen pattern) */}
                                    <ScrollView contentContainerStyle={{
                                        flexDirection: 'row',
                                        flexWrap: 'wrap',
                                        justifyContent: 'flex-start',
                                        paddingVertical: 10,
                                    }}>
                                        {itemList.filter(
                                            (item) =>
                                                item.ItemName.toLowerCase().includes(searchText.toLowerCase()) ||
                                                item.ItemID.toLowerCase().includes(searchText.toLowerCase())
                                        ).map((item) => (
                                            <TouchableOpacity
                                                key={item.ItemID}
                                                style={{
                                                    backgroundColor: '#FDC500',
                                                    borderRadius: 8,
                                                    padding: 10,
                                                    width: '23%',
                                                    minHeight: 100,
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    marginBottom: 10,
                                                    marginRight: '2%',
                                                }}
                                                onPress={() => handleItemSelect(item)}
                                            >
                                                <Text style={{
                                                    fontSize: 14,
                                                    color: 'rgb(2, 2, 2)',
                                                    textAlign: 'center',
                                                }}>{item.ItemName}</Text>
                                            </TouchableOpacity>
                                        ))}
                                    </ScrollView>

                                    {/* Close X Button (following indent screen pattern) */}
                                    <TouchableOpacity
                                        onPress={() => setModalVisible(false)}
                                        style={{
                                            position: 'absolute',
                                            top: 10,
                                            right: 10,
                                            width: 30,
                                            height: 30,
                                            backgroundColor: '#000',
                                            borderRadius: 15,
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            zIndex: 1000,
                                        }}
                                    >
                                        <Text style={{ color: '#fff', fontSize: 18, fontWeight: 'bold' }}>×</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </Modal>

                        {batchEnabled && (
                            <>
                                <TextInput
                                    placeholder="Choose Batch"
                                    style={styles.inputBox}
                                    value={batch}
                                    editable={false}
                                />
                                <TouchableOpacity style={styles.selectBtn} onPress={() => setBatchModalVisible(true)}>
                                    <Text style={styles.btnTextWhite}>Select</Text>
                                </TouchableOpacity>

                                <Modal visible={batchModalVisible} transparent animationType="slide">
                                    <View style={{
                                        flex: 1,
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        backgroundColor: 'rgba(0,0,0,0.3)'
                                    }}>
                                        <View style={{
                                            width: '90%',
                                            height: '50%',
                                            backgroundColor: '#fff',
                                            borderRadius: 10,
                                            padding: 16,
                                        }}>
                                            <FlatList
                                                data={batchList}
                                                keyExtractor={(item) => item.BatchNumber}
                                                renderItem={({ item }) => (
                                                    <TouchableOpacity
                                                        style={{
                                                            padding: 10,
                                                            marginBottom: 8,
                                                            backgroundColor: '#eee',
                                                            borderRadius: 6,
                                                        }}
                                                        onPress={() => {
                                                            setBatch(item.BatchNumber);
                                                            setStockQty(item.StockQty || 0);
                                                            setStockAltQty(item.StockAltQty || 0);

                                                            const qty = item.StockQty || 0;
                                                            const altQty = item.StockAltQty || 0;

                                                            // Auto-fill based on flags
                                                            if (sellByWeight && altQtyEnabled) {
                                                                setKgs(qty.toString());
                                                                setNos(altQty.toString());
                                                            } else if (sellByWeight) {
                                                                setKgs(qty.toString());
                                                                setNos('');
                                                            } else {
                                                                setNos(qty.toString());
                                                                setKgs('');
                                                            }

                                                            setBatchModalVisible(false);
                                                        }}

                                                    >
                                                        <Text>{item.BatchNumber} - Stock: {item.StockQty}</Text>
                                                    </TouchableOpacity>
                                                )}
                                            />
                                            <TouchableOpacity
                                                style={{
                                                    marginTop: 10,
                                                    backgroundColor: 'red',
                                                    padding: 10,
                                                    borderRadius: 8,
                                                    alignItems: 'center'
                                                }}
                                                onPress={() => setBatchModalVisible(false)}
                                            >
                                                <Text style={{ color: 'white' }}>Close</Text>
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                </Modal>
                            </>
                        )}

                        {/* View Stock Take Modal (following indent view popup pattern) */}
                        <Modal
                            animationType="slide"
                            transparent={true}
                            visible={viewModalVisible}
                            onRequestClose={() => setViewModalVisible(false)}
                        >
                            <View style={styles.dialog_overlay}>
                                <View style={styles.dialog_container}>
                                    {/* Header */}
                                    <Text style={styles.dialog_title}>View Stock Take</Text>

                                    {/* Search and Date Range Selection in same row (copied from indent screen) */}
                                    <View style={{ flexDirection: 'row', gap: 8, marginBottom: 16 }}>
                                        {/* Search Input */}
                                        <TextInput
                                            placeholder="Search..."
                                            value={searchQuery}
                                            onChangeText={(text) => {
                                                setSearchQuery(text);
                                            }}
                                            style={{
                                                borderWidth: 1,
                                                borderColor: '#ccc',
                                                borderRadius: 8,
                                                paddingHorizontal: 10,
                                                paddingVertical: Platform.OS === 'ios' ? 8 : 6,
                                                height: 40,
                                                flex: 1,
                                                fontSize: 14,
                                            }}
                                        />

                                        {/* From Date */}
                                        <TouchableOpacity
                                            onPress={() => setShowFromDatePicker(true)}
                                            style={{
                                                borderWidth: 1,
                                                borderColor: '#ccc',
                                                borderRadius: 8,
                                                paddingHorizontal: 10,
                                                paddingVertical: Platform.OS === 'ios' ? 8 : 6,
                                                height: 40,
                                                justifyContent: 'center',
                                                flex: 1,
                                            }}
                                        >
                                            <Text style={{ fontSize: 12, color: '#666' }}>From Date</Text>
                                            <Text style={{ fontSize: 14, fontWeight: '500' }}>
                                                {fromDate ? fromDate.toLocaleDateString() : 'Select Date'}
                                            </Text>
                                        </TouchableOpacity>

                                        {/* To Date */}
                                        <TouchableOpacity
                                            onPress={() => setShowToDatePicker(true)}
                                            style={{
                                                borderWidth: 1,
                                                borderColor: '#ccc',
                                                borderRadius: 8,
                                                paddingHorizontal: 10,
                                                paddingVertical: Platform.OS === 'ios' ? 8 : 6,
                                                height: 40,
                                                justifyContent: 'center',
                                                flex: 1,
                                            }}
                                        >
                                            <Text style={{ fontSize: 12, color: '#666' }}>To Date</Text>
                                            <Text style={{ fontSize: 14, fontWeight: '500' }}>
                                                {toDate ? toDate.toLocaleDateString() : 'Select Date'}
                                            </Text>
                                        </TouchableOpacity>
                                    </View>

                                    {/* From Date Picker */}
                                    {showFromDatePicker && (
                                        <DateTimePicker
                                            value={fromDate || new Date()}
                                            mode="date"
                                            display="default"
                                            onChange={(event, selectedDate) => {
                                                setShowFromDatePicker(false);
                                                if (selectedDate) {
                                                    setFromDate(selectedDate);
                                                }
                                            }}
                                        />
                                    )}

                                    {/* To Date Picker */}
                                    {showToDatePicker && (
                                        <DateTimePicker
                                            value={toDate || new Date()}
                                            mode="date"
                                            display="default"
                                            onChange={(event, selectedDate) => {
                                                setShowToDatePicker(false);
                                                if (selectedDate) {
                                                    setToDate(selectedDate);
                                                }
                                            }}
                                        />
                                    )}

                                    {/* Stock Takes List (following indent screen grid pattern) */}
                                    <ScrollView style={styles.dialog_content}>
                                        <View style={styles.dialog_grid}>
                                            {loading && (
                                                <View style={{ width: '100%', alignItems: 'center', padding: 20 }}>
                                                    <ActivityIndicator size="large" color="#0000ff" />
                                                </View>
                                            )}
                                            {!loading && filteredStockTakes.map((stockTake, index) => {
                                                // Calculate margin for proper 3-button layout (copied from indent screen)
                                                const isThirdInRow = (index + 1) % 3 === 0;
                                                const marginRightValue = isThirdInRow ? 0 : '2%';

                                                return (
                                                    <TouchableOpacity
                                                        key={index}
                                                        onPress={() => {
                                                            handleStockTakeSelect(stockTake.StockAdjustId);
                                                        }}
                                                        style={{
                                                            backgroundColor: '#FDC500',
                                                            padding: 15,
                                                            borderRadius: 12,
                                                            width: '32%',
                                                            aspectRatio: 1,
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                            marginBottom: 15,
                                                            marginRight: marginRightValue,
                                                            shadowColor: '#000',
                                                            shadowOffset: {
                                                                width: 0,
                                                                height: 2,
                                                            },
                                                            shadowOpacity: 0.25,
                                                            shadowRadius: 3.84,
                                                            elevation: 5,
                                                        }}
                                                    >
                                                        <Text style={{
                                                            color: '#000',
                                                            textAlign: 'center',
                                                            fontWeight: 'bold',
                                                            fontSize: 14,
                                                        }}>
                                                            {stockTake.DocID}
                                                        </Text>
                                                    </TouchableOpacity>
                                                );
                                            })}
                                        </View>
                                    </ScrollView>

                                    {/* Close X Button (following indent pattern) */}
                                    <TouchableOpacity
                                        onPress={() => setViewModalVisible(false)}
                                        style={styles.dialog_closeButton}
                                    >
                                        <Text style={styles.dialog_closeButtonText}>×</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </Modal>

                        <View style={styles.dropdownWrapperPort}>
                            <Dropdown
                                style={styles.dropdownPort}
                                data={ports}
                                labelField="label"
                                valueField="value"
                                placeholder="Select Port"
                                value={port}
                                onChange={item => setPort(item.value)}
                                selectedTextStyle={styles.selectedTextStyle}
                                placeholderStyle={styles.placeholderStyle}
                            />
                        </View>
                    </View>

                    <View style={styles.inputRow}>
                        <TextInput
                            placeholder="New Nos"
                            style={[
                                styles.inputSmall,
                                (!sellByWeight && !altQtyEnabled) ? {} : { backgroundColor: '#e0e0e0' }
                            ]}
                            keyboardType="numeric"
                            value={newNos}
                            onChangeText={setNewNos}
                            editable={!sellByWeight && !altQtyEnabled}
                        />
                        <TextInput
                            placeholder="New Wt"
                            style={[
                                styles.inputSmall,
                                sellByWeight ? {} : { backgroundColor: '#e0e0e0' }
                            ]}
                            keyboardType="numeric"
                            value={newWt}
                            onChangeText={setNewWt}
                            editable={sellByWeight}
                        />
                        <TextInput
                            placeholder="Nos"
                            style={[
                                styles.inputSmall,
                                (!sellByWeight && !altQtyEnabled) ? {} : { backgroundColor: '#e0e0e0' }
                            ]}
                            keyboardType="numeric"
                            value={nos}
                            editable={false}
                        />
                        <TextInput
                            placeholder="Wt(Kg)"
                            style={[
                                styles.inputSmall,
                                sellByWeight ? {} : { backgroundColor: '#e0e0e0' }
                            ]}
                            keyboardType="numeric"
                            value={kgs}
                            editable={false}
                        />
                    </View>

                    <View style={styles.inputRow}>
                        <TextInput
                            placeholder="Adjusted Nos"
                            style={[
                                styles.inputSmall,
                                (!sellByWeight && !altQtyEnabled) ? {} : { backgroundColor: '#e0e0e0' }
                            ]}
                            keyboardType="numeric"
                            value={adjustedNos}
                            editable={false}
                        />
                        <TextInput
                            placeholder="Adjusted Wt"
                            style={[
                                styles.inputSmall,
                                sellByWeight ? {} : { backgroundColor: '#e0e0e0' }
                            ]}
                            keyboardType="numeric"
                            value={adjustedWt}
                            editable={false}
                        />
                    </View>


                    <View style={styles.row}>
                        <TextInput
                            placeholder="Remarks"
                            style={styles.remarkInput}
                            value={remarks}
                            onChangeText={setRemarks}
                        />
                        <TouchableOpacity
                            style={styles.addBtn}
                            onPress={handleAddItem}
                        >
                            <Text style={styles.btnText}>Add</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.clearBtn} onPress={handleClearFields}>
                            <Text style={styles.btnText}>Clear</Text>
                        </TouchableOpacity>
                    </View>
                </View>

                {/* Table Section */}
                <View style={styles.section}>
                    <View style={styles.tableContainer}>
                        <ScrollView horizontal>
                            <View>
                                <View style={styles.tableHeader}>
                                    <Text style={[styles.tableHeaderCell, styles.selectColumn]}>Select</Text>
                                    <Text style={[styles.tableHeaderCell, styles.lineNoColumn]}>Line No</Text>
                                    <Text style={[styles.tableHeaderCell, styles.itemIdColumn]}>Item ID</Text>
                                    <Text style={[styles.tableHeaderCell, styles.itemNameColumn]}>Item Name</Text>
                                    <Text style={[styles.tableHeaderCell, styles.batchColumn]}>Batch No</Text>
                                    <Text style={[styles.tableHeaderCell, styles.stockAltQtyColumn]}>StockAltQty</Text>
                                    <Text style={[styles.tableHeaderCell, styles.stockQtyColumn]}>StockQty</Text>
                                    <Text style={[styles.tableHeaderCell, styles.newNosColumn]}>New Nos</Text>
                                    <Text style={[styles.tableHeaderCell, styles.newKgsColumn]}>New Kgs</Text>
                                    <Text style={[styles.tableHeaderCell, styles.remarksColumn]}>Remarks</Text>
                                </View>
                                {items.map((item, index) => (
                                    <TouchableOpacity
                                        key={item.id}
                                        style={[
                                            styles.tableRow,
                                            editingItemIndex === index && styles.tableRowEditing
                                        ]}
                                        onPress={() => handleItemRowClick(item, index)}
                                    >
                                        <View style={[styles.tableCell, styles.selectColumn]}>
                                            <TouchableOpacity
                                                style={[
                                                    styles.customCheckbox,
                                                    selectedItems.includes(item.id) && styles.customCheckboxSelected
                                                ]}
                                                onPress={(e) => {
                                                    e.stopPropagation(); // Prevent row click when selecting checkbox
                                                    setSelectedItems(prev =>
                                                        prev.includes(item.id)
                                                            ? prev.filter(i => i !== item.id)
                                                            : [...prev, item.id]
                                                    );
                                                }}
                                            >
                                                {selectedItems.includes(item.id) && (
                                                    <Icon name="check" size={16} color="white" />
                                                )}
                                            </TouchableOpacity>
                                        </View>
                                        <Text style={[styles.tableCell, styles.lineNoColumn]}>{item.lineNo}</Text>
                                        <Text style={[styles.tableCell, styles.itemIdColumn]}>{item.itemId}</Text>
                                        <Text style={[styles.tableCell, styles.itemNameColumn]}>{item.itemName}</Text>
                                        <Text style={[styles.tableCell, styles.batchColumn]}>{item.batch || '-'}</Text>
                                        <Text style={[styles.tableCell, styles.stockAltQtyColumn]}>{item.stockAltQty}</Text>
                                        <Text style={[styles.tableCell, styles.stockQtyColumn]}>{item.stockQty}</Text>
                                        <Text style={[styles.tableCell, styles.newNosColumn]}>{item.newNos || '0'}</Text>
                                        <Text style={[styles.tableCell, styles.newKgsColumn]}>{item.newKgs || '0'}</Text>
                                        <Text style={[styles.tableCell, styles.remarksColumn]}>{item.remarks || '-'}</Text>
                                    </TouchableOpacity>
                                ))}
                            </View>
                        </ScrollView>

                        {/* Table Footer */}
                        <View style={styles.footerRow}>
                            {/* Delete Button */}
                            <TouchableOpacity style={styles.deleteBtn} onPress={deleteSelectedItems}>
                                <Text style={styles.footerText}>Delete Selected Row</Text>
                            </TouchableOpacity>

                        </View>
                    </View>
                    {/* 
          <TouchableOpacity style={styles.deleteBtn} onPress={deleteSelectedItems}>
            <Text style={styles.deleteBtnText}>Delete Selected</Text>
          </TouchableOpacity> */}
                </View>
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#e9e9e9',

    },
    scrollContent: {
        paddingBottom: 20,
    },

    headerRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        backgroundColor: '#e9e9e9',
        padding: 20,
    },

    headerTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        flex: 1,
        marginLeft: 10,
    },
    actionButtons: {
        flexDirection: 'row',
        gap: 5,
        flexWrap: 'wrap',
    },

    newBtn: {
        backgroundColor: '#E2E3E5',
        paddingVertical: 14,
        paddingHorizontal: 25,
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 8,
    },
    viewBtn: {
        backgroundColor: '#E2E3E5',
        paddingVertical: 14,
        paddingHorizontal: 25,
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 8,
    },
    saveBtn: {
        backgroundColor: '#28A745',
        paddingVertical: 14,
        paddingHorizontal: 25,
        borderRadius: 8,
    },
    saveBtnDisabled: {
        backgroundColor: '#cccccc',
    },

    cancelBtn: {
        backgroundColor: 'red',
        paddingVertical: 14,
        paddingHorizontal: 25,
        borderRadius: 8,
    },

    actionText: {
        fontWeight: 'bold',
        fontSize: 16,
    },
    saveText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16,
    },
    saveTextDisabled: {
        color: '#999999',
    },
    cancelText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16,
    },


    section: {
        backgroundColor: '#e9e9e9',
        paddingHorizontal: 10,
        marginBottom: 0,

    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 10,
        marginTop: 0, // ↓ Ensures no extra space on top
        color: '#000',
    },

    inputRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 8,
        marginBottom: 12,
        width: '100%',
    },

    inputBox: {
        flex: 1,
        borderWidth: 1,
        borderColor: '#999',
        borderRadius: 6,
        paddingHorizontal: 10,
        height: 50, // Increased from 40
        backgroundColor: '#fff',
    },

    dropdownBox: {
        flex: 1,
        minWidth: '30%',
        borderWidth: 1,
        borderColor: '#aaa',
        borderRadius: 6,
        paddingHorizontal: 10,
        height: 50, // Increased from 40
        backgroundColor: '#fff',
    },

    dropdownWrapper: {
        flex: 1,
        minWidth: '30%',
    },

    dropdownWrapperPort: {
        width: 120,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 6,
        backgroundColor: '#fff',
    },


    dropdown: {
        height: 50, // Increased from 40
        borderColor: '#aaa',
        borderWidth: 1,
        borderRadius: 6,
        paddingHorizontal: 8,
        backgroundColor: '#fff',
    },
    dropdownPort: {
        height: 50, // Increased from 40
        paddingHorizontal: 8,
    },
    selectedTextStyle: {
        fontSize: 14,
    },
    placeholderStyle: {
        fontSize: 14,
        color: '#888',
    },
    selectBtn: {
        paddingHorizontal: 12,
        paddingVertical: 12, // Increased
        backgroundColor: '#1E2D52',
        borderRadius: 6,
        height: 50, // Increased from 40
        justifyContent: 'center',
        alignItems: 'center',
    },
    transitBtn: {
        backgroundColor: '#1E2D52',
        paddingHorizontal: 12,
        paddingVertical: 12, // Increased
        borderRadius: 6,
        height: 50, // Increased from 40
        justifyContent: 'center',
        alignItems: 'center',
    },
    newBtnSmall: {
        backgroundColor: '#28A745',
        height: 50, // Increased from 40
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 12,
        borderRadius: 6,
        width: 70,
    },

    remarkInput: {
        height: 50,
        backgroundColor: '#fff',
        flex: 1,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 5,
        paddingHorizontal: 10,
        marginRight: 8,
    },

    remarkInputlocation: {
        height: 50,
        backgroundColor: '#fff',
        flex: 1,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 5,
        paddingHorizontal: 10,
    },


    addBtn: {
        backgroundColor: 'green',
        width: 90,
        height: 50,
        justifyContent: 'center',    // center vertically
        alignItems: 'center',        // center horizontally
        borderRadius: 5,
        marginRight: 10,             // gap between Add and Clear
    },

    clearBtn: {
        backgroundColor: 'red',
        width: 90,
        height: 50,
        justifyContent: 'center',    // center vertically
        alignItems: 'center',        // center horizontally
        borderRadius: 5,
    },

    btnText: {
        color: '#fff',
        fontWeight: 'bold',
        textAlign: 'center',
    },
    LocationclearBtn: {
        backgroundColor: 'red',
        paddingHorizontal: 12,
        paddingVertical: 12, // Increased
        height: 50, // Increased from 40
        borderRadius: 6,
        width: 70,
        justifyContent: 'center',
        alignItems: 'center',
    },

    inputSmall: {
        flex: 1,
        minWidth: '22%',
        height: 50, // Increased from 40
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 6,
        paddingHorizontal: 8,
        backgroundColor: '#fff',
    },
    btnText: {
        color: '#fff',
        fontWeight: 'bold',
        textAlign: 'center',
        fontSize: 14, // Added for better visibility
    },
    btnTextWhite: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 14, // Added for better visibility
    },
    inputWithButton: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
        minWidth: '48%',
        gap: 8,
    },
    row: {
        flexDirection: 'row',
        justifyContent: 'space-between',

    },


    tableScrollContainer: {
        maxHeight: 500, // Adjust based on your row height. ~50px * 4 rows
        borderWidth: 1,
        borderColor: '#ccc',

    },

    tableContainer: {
        marginTop: 10,
        overflow: 'hidden',
        borderRadius: 5,
        marginBottom: 10,

    },
    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#002b5c',
        paddingVertical: 14,
        paddingHorizontal: 8,
        alignItems: 'center',
    },
    headerCell: {
        flex: 1,
        fontWeight: '600',
        color: 'white',
        fontSize: 14,
        textAlign: 'center',
    },
    tableRow: {
        flexDirection: 'row',
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderColor: '#f0f0f0',
        alignItems: 'center',
        backgroundColor: '#fff',
    },
    tableRowEven: {
        backgroundColor: '#f9f9f9',
    },
    cell: {
        flex: 1,
        paddingHorizontal: 8,
        textAlign: 'center',
        fontSize: 13,
        color: '#333',
    },
    checkboxCell: {
        flex: 0.4,
        textAlign: 'center',
    },
    lineNumberCell: {
        flex: 0.7,
    },
    itemNameCell: {
        flex: 1.5,
        textAlign: 'left',
    },
    nosCell: {
        flex: 0.8,
    },
    kgsCell: {
        flex: 0.8,
    },
    remarksCell: {
        flex: 1.2,
        textAlign: 'left',
    },


    footerRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 10,
        backgroundColor: '#fff',
        borderColor: '#ccc',
        flexWrap: 'wrap',
    },

    footerBox: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#dcdcdc',
        paddingHorizontal: 10,
        paddingVertical: 8,
        borderRadius: 6,
        height: 40,
        marginLeft: 8,
    },

    footerBoxHighlight: {
        backgroundColor: '#bcd4ff',
    },

    footerLabel: {
        color: '#333',
        fontWeight: '600',
    },

    footerLabelHighlight: {
        color: '#003f8a',
    },

    footerValue: {
        color: '#000',
        fontWeight: '600',
        marginLeft: 4,
    },

    deleteBtn: {
        backgroundColor: 'red',
        width: 200,
        height: 50,
        justifyContent: 'center',    // center vertically
        alignItems: 'center',        // center horizontally
        borderRadius: 5,
        marginRight: 10,
    },

    footerText: {
        color: '#fff',
        fontWeight: 'bold',
    },

    updateBtn: {
        backgroundColor: '#ffa500',
        width: 90,
        height: 50,
        justifyContent: 'center',    // center vertically
        alignItems: 'center',        // center horizontally
        borderRadius: 5,
        marginRight: 10,
    },
    actionCell: {
        width: 50,
        justifyContent: 'center',
        alignItems: 'center',
    },

    // New table styles for horizontal scrolling
    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#002b5c',
        padding: 8
    },
    tableHeaderCell: {
        fontWeight: 'bold',
        padding: 6,
        color: 'white',
        textAlign: 'center',
        justifyContent: 'center',
        alignItems: 'center'
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderColor: '#ccc',
        padding: 6,
        backgroundColor: '#fff',
        alignItems: 'center'
    },
    tableCell: {
        padding: 6,
        textAlign: 'center',
        justifyContent: 'center',
        alignItems: 'center'
    },

    // Column-specific styles for proper sizing
    selectColumn: {
        width: 60,
        justifyContent: 'center',
        alignItems: 'center'
    },
    lineNoColumn: {
        width: 70,
        textAlign: 'center'
    },
    itemIdColumn: {
        width: 100,
        textAlign: 'center'
    },
    itemNameColumn: {
        width: 150,
        textAlign: 'left'
    },
    batchColumn: {
        width: 100,
        textAlign: 'center'
    },
    stockAltQtyColumn: {
        width: 100,
        textAlign: 'center'
    },
    stockQtyColumn: {
        width: 100,
        textAlign: 'center'
    },
    newNosColumn: {
        width: 80,
        textAlign: 'center'
    },
    newKgsColumn: {
        width: 80,
        textAlign: 'center'
    },
    remarksColumn: {
        width: 120,
        textAlign: 'left'
    },

    // Custom checkbox styles
    customCheckbox: {
        width: 24,
        height: 24,
        borderWidth: 2,
        borderColor: '#002b5c',
        borderRadius: 4,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'transparent'
    },
    customCheckboxSelected: {
        backgroundColor: '#002b5c'
    },

    // Table row editing style
    tableRowEditing: {
        backgroundColor: '#e6f3ff',
    },

    // View modal styles (following indent view popup pattern)
    dialog_overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    dialog_container: {
        backgroundColor: '#fff',
        borderRadius: 15,
        padding: 20,
        width: '90%',
        maxHeight: '80%',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    dialog_title: {
        fontSize: 20,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 20,
        color: '#333',
    },
    dialog_searchContainer: {
        marginBottom: 15,
    },
    dialog_searchInput: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 10,
        fontSize: 16,
        backgroundColor: '#f9f9f9',
        minHeight: 40, // Ensure consistent height with date buttons
    },
    // New styles for search and date in one row
    dialog_searchDateContainer: {
        flexDirection: 'row',
        marginBottom: 20,
        gap: 10,
        alignItems: 'flex-end', // Align all items to bottom for even layout
    },
    dialog_searchWrapper: {
        flex: 2, // Give search more space
    },
    dialog_dateWrapper: {
        flexDirection: 'row',
        gap: 10,
        flex: 3, // Give date pickers more space
    },
    dialog_dateGroup: {
        flex: 1, // Make both date groups equal width
        alignItems: 'center',
    },
    dialog_dateLabel: {
        fontSize: 12,
        fontWeight: 'bold',
        marginBottom: 5,
        color: '#333',
        textAlign: 'center',
    },
    dialog_dateContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 20,
        gap: 10,
    },
    dialog_dateButton: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 10, // Match search input padding
        alignItems: 'center',
        backgroundColor: '#f9f9f9',
        minHeight: 40, // Ensure consistent height with search input
    },
    dialog_dateText: {
        fontSize: 14,
        color: '#333',
    },
    dialog_scrollView: {
        maxHeight: 300,
        marginBottom: 20,
    },
    dialog_buttonsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        paddingHorizontal: 5,
    },
    dialog_loadingContainer: {
        width: '100%',
        alignItems: 'center',
        padding: 20,
    },
    dialog_stockTakeButton: {
        backgroundColor: '#FDC500',
        padding: 15,
        borderRadius: 12,
        width: '32%',
        aspectRatio: 1,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 15,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    dialog_stockTakeButtonText: {
        color: '#000',
        textAlign: 'center',
        fontWeight: 'bold',
        fontSize: 16,
    },
    // Grid layout for buttons (copied from indent screen)
    dialog_content: {
        maxHeight: 300,
    },
    dialog_grid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'flex-start', // This ensures buttons start from left to right
        paddingHorizontal: 5,
    },
    dialog_closeButton: {
        position: 'absolute',
        top: 10,
        right: 10,
        width: 30,
        height: 30,
        backgroundColor: '#000',
        borderRadius: 15,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
    },
    dialog_closeButtonText: {
        color: '#fff',
        fontSize: 18,
        fontWeight: 'bold',
    },


});

export default StockTake;
